# EmailJS and Cloudinary Configuration Setup

This document explains how to configure EmailJS and Cloudinary in your portfolio admin system.

## Overview

The admin system now supports configuration for:
- **EmailJS**: For sending emails from your portfolio contact forms
- **Cloudinary**: For image storage and management

These configurations can be set per user through the admin panel's configuration module.

## Backend Configuration

### Environment Variables

Add the following to your `admin-backend/.env` file:

```env
# EmailJS Configuration
EMAILJS_SERVICE_ID=service_xxxxxxx
EMAILJS_TEMPLATE_ID=template_xxxxxxx
EMAILJS_PUBLIC_KEY=your_emailjs_public_key
EMAILJS_PRIVATE_KEY=your_emailjs_private_key

# Cloudinary Configuration (if not already set)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
```

### Database Schema

The configuration schema now includes these new fields:

```typescript
// EmailJS Configuration
emailjsServiceId?: string
emailjsTemplateId?: string
emailjsPublicKey?: string
emailjsPrivateKey?: string

// Cloudinary Configuration
cloudinaryCloudName?: string
cloudinaryApiKey?: string
cloudinaryApiSecret?: string
```

## Frontend Configuration

### Environment Variables

Add the following to your `admin-frontend/.env` file:

```env
# EmailJS Configuration (Public Key only for frontend)
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=your_emailjs_public_key

# Cloudinary Configuration (Public settings only for frontend)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
```

### Admin Panel

You can now configure EmailJS and Cloudinary settings through the admin panel:

1. Navigate to **Configuration** → **Add/Edit Configuration**
2. Scroll down to the **EmailJS Configuration** section
3. Fill in your EmailJS credentials:
   - Service ID
   - Template ID
   - Public Key
   - Private Key
4. Scroll to the **Cloudinary Configuration** section
5. Fill in your Cloudinary credentials:
   - Cloud Name
   - API Key
   - API Secret

## Getting Your Credentials

### EmailJS Setup

1. Go to [EmailJS](https://www.emailjs.com/)
2. Create an account or log in
3. Create a new service (Gmail, Outlook, etc.)
4. Create an email template
5. Get your credentials:
   - **Service ID**: Found in your EmailJS dashboard
   - **Template ID**: Found in your email templates
   - **Public Key**: Found in Account → API Keys
   - **Private Key**: Found in Account → API Keys

### Cloudinary Setup

1. Go to [Cloudinary](https://cloudinary.com/)
2. Create an account or log in
3. Go to your Dashboard
4. Get your credentials:
   - **Cloud Name**: Your cloud name
   - **API Key**: Your API key
   - **API Secret**: Your API secret (keep this secure)

## Security Notes

- **Private Keys**: EmailJS private key and Cloudinary API secret should be kept secure
- **Frontend**: Only public keys/cloud names should be exposed in frontend environment variables
- **Backend**: All sensitive credentials should be stored in backend environment variables only

## Usage in Your Portfolio

Once configured, these settings will be available for:
- **EmailJS**: Sending contact form emails from your portfolio
- **Cloudinary**: Storing and serving images in your portfolio

The configuration is per-user, so different users can have different EmailJS and Cloudinary settings.

## Troubleshooting

1. **EmailJS not working**: Check that all four EmailJS fields are filled correctly
2. **Cloudinary not working**: Verify cloud name, API key, and secret are correct
3. **Configuration not saving**: Ensure you have selected a user for the configuration
4. **Environment variables**: Make sure to restart your servers after updating .env files

## Example Configuration

Here's an example of how your configuration might look:

```json
{
  "emailjsServiceId": "service_abc123",
  "emailjsTemplateId": "template_xyz789",
  "emailjsPublicKey": "user_public_key_here",
  "emailjsPrivateKey": "user_private_key_here",
  "cloudinaryCloudName": "my-portfolio-cloud",
  "cloudinaryApiKey": "123456789012345",
  "cloudinaryApiSecret": "secret_key_here"
}
```
