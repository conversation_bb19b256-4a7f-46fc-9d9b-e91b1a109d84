/** @type {import('next').NextConfig} */
const nextConfig = {
  basePath: process.env.BASEPATH,

  // Disable font optimization to prevent network requests during build
  optimizeFonts: false,

  // Disable image optimization to prevent network requests during build
  images: {
    disableStaticImages: true,
    unoptimized: true
  },

  // Disable external requests during build
  experimental: {
    // Disable external requests during build
    externalDir: true
  },
  eslint: {
    ignoreDuringBuilds: true
  },

  // Enable standalone output for Docker deployment
  output: 'standalone'
}

module.exports = nextConfig
