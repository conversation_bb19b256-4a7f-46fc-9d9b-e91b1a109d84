import { Module } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './modules/user/user.module';
import { EducationModule } from './modules/education/education.module';
import { ProjectsModule } from './modules/projects/projects.module';
import { DbModule } from '@app/db/db.module';
import { AuthModule } from './modules/auth/auth.module';
import { BioModule } from './modules/bio/bio.module';
import { ContactModule } from './modules/contact/contact.module';
import { CompanyModule } from './modules/company/company.module';
import { InterestModule } from './modules/interest/interest.module';
import { ObjectiveModule } from './modules/objective/objective.module';
import { SkillModule } from './modules/skill/skill.module';
import { ConfigModule } from '@nestjs/config';
import { SocialMediaModule } from './modules/social-media/social-media.module';
import { UserConfigModule } from './modules/config/config.module';
import { RepositoryModule } from '@app/repository';
import { QueueModule, VisitorLoggingInterceptor } from '@app/common';
import { RoleModule } from './modules/role/role.module';
import { UserRoleModule } from './modules/user-role/user-role.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env'],
      cache: true,
      expandVariables: true,
    }),
    DbModule,
    RepositoryModule,
    QueueModule,
    UserModule,
    EducationModule,
    ProjectsModule,
    AuthModule,
    BioModule,
    ContactModule,
    CompanyModule,
    InterestModule,
    ObjectiveModule,
    SkillModule,
    SocialMediaModule,
    UserConfigModule,
    RoleModule,
    UserRoleModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: VisitorLoggingInterceptor,
    },
  ],
})
export class AppModule {}
