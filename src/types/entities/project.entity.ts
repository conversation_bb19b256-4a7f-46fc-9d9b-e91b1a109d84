import type { SkillEntity } from '@/types/entities/skill.entity'

export type ProjectEntity = {
  id?: string
  name: string
  coverImages: string[]
  projectDescription: string
  startedAt?: Date
  endedAt?: Date
  githubLink?: string
  projectLiveLink?: string
  skill: SkillEntity[] // This maps the referenced Skill documents
  companyWebsite?: string
  details: string
  active: boolean
  createdAt?: Date
  updatedAt?: Date
}
