'use client'

// React Imports
import { useEffect, useState } from 'react'

// Next Imports
import Link from 'next/link'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Button from '@mui/material/Button'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'

// Component Imports
import UserRolesTable from './UserRolesTable'

// Type Imports
import type { UserRoleWithDetails } from '@/types/user-role'

const AllUserRoles = () => {
  // States
  const [userRoles, setUserRoles] = useState<UserRoleWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [notification, setNotification] = useState<{
    show: boolean
    message: string
    type: 'success' | 'error' | 'warning' | 'info'
  }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Fetch user roles
  const fetchUserRoles = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/user-roles')

      if (!response.ok) {
        throw new Error(`Failed to fetch user roles: ${response.statusText}`)
      }

      const data = await response.json()
      setUserRoles(data.data || data)
    } catch (err) {
      console.error('Error fetching user roles:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch user roles')
    } finally {
      setLoading(false)
    }
  }

  // Handle delete success
  const handleDeleteSuccess = (deletedUserRoleId: string) => {
    setUserRoles(prevUserRoles => prevUserRoles.filter(userRole => userRole.id !== deletedUserRoleId))
    setNotification({
      show: true,
      message: 'User role assignment deleted successfully',
      type: 'success'
    })
  }

  // Handle status change
  const handleStatusChange = (userRoleId: string, newStatus: boolean) => {
    setUserRoles(prevUserRoles =>
      prevUserRoles.map(userRole => (userRole.id === userRoleId ? { ...userRole, isActive: newStatus } : userRole))
    )
    setNotification({
      show: true,
      message: 'User role status updated successfully',
      type: 'success'
    })
  }

  // Initial fetch
  useEffect(() => {
    fetchUserRoles()
  }, [])

  return (
    <Card>
      <CardHeader
        title='All User Role Assignments'
        action={
          <Button
            variant='contained'
            startIcon={<i className='ri-add-line'></i>}
            component={Link}
            href='/user-roles?tab=user-role'
          >
            Assign Role
          </Button>
        }
      />
      <CardContent>
        <UserRolesTable
          userRoles={userRoles}
          loading={loading}
          error={error}
          onDeleteSuccess={handleDeleteSuccess}
          onStatusChange={handleStatusChange}
        />
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default AllUserRoles
