import { Module } from '@nestjs/common';
import { ContactService } from './contact.service';
import { ContactController } from './contact.controller';
import { JwtService } from '@nestjs/jwt';
import { RepositoryModule } from '@app/repository';
import { CloudinaryModule } from '@app/common';
import { UserConfigModule } from '../config/config.module';

@Module({
  imports: [RepositoryModule, CloudinaryModule, UserConfigModule],
  controllers: [ContactController],
  providers: [ContactService, JwtService],
})
export class ContactModule {}
