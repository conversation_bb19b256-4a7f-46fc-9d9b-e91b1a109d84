import { IDataServices } from '@app/repository';
import { BioPresenter } from '../presenter';
import { extractTenantUserFromRequest } from '@app/common';
import { ConfigService } from '../../config/config.service';
import { Logger } from '@nestjs/common';

export const getAllForTenant = async (
  request: any,
  db: IDataServices,
  configService: ConfigService,
) => {
  const logger = new Logger('getAllForTenant');

  try {
    // Extract tenant user from request
    const tenantUser = await extractTenantUserFromRequest(
      request,
      configService,
    );

    if (!tenantUser) {
      logger.warn('No tenant user found for request');
      return [];
    }

    logger.log(`Fetching bio for tenant user: ${tenantUser.id || tenantUser}`);

    // Fetch bio for this specific user
    const bio = await db.bios.findAll({
      user: tenantUser,
      active: true,
    });

    logger.log(`Found ${bio.length} bio records for tenant user`);

    // Apply presenter and return only active bio records
    return bio
      .filter((bioRecord) => bioRecord.active)
      .map((bioRecord) => new BioPresenter(bioRecord));
  } catch (error) {
    logger.error('Error in getAllForTenant:', error);
    return [];
  }
};
