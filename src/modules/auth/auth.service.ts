import { Injectable } from '@nestjs/common';
import { LoginDto, RegistrationDto } from './dto';
import { IDataServices } from '@app/repository';
import { login, userRegistration } from './services';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class AuthService {
  registration(
    registrationDto: RegistrationDto,
    db: IDataServices,
    configService: ConfigService,
    jwtService: JwtService,
  ) {
    return userRegistration(registrationDto, db, configService, jwtService);
  }

  login(
    loginDto: LoginDto,
    db: IDataServices,
    configService: ConfigService,
    jwtService: JwtService,
  ) {
    return login(loginDto, db, configService, jwtService);
  }
}
