import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateProjectDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  projectDescription: string;

  @IsOptional()
  @IsDateString()
  startedAt: Date;

  @IsOptional()
  @IsDateString()
  endedAt: Date;

  @IsOptional()
  @IsUrl()
  githubLink: string;

  @IsOptional()
  @IsUrl()
  projectLiveLink: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => {
    if (Array.isArray(value)) {
      return value;
    }
    if (
      typeof value === 'string' &&
      value.startsWith('[') &&
      value.endsWith(']')
    ) {
      try {
        const parsed = JSON.parse(value);
        return parsed;
      } catch (e) {
        return [value];
      }
    }
    if (typeof value === 'string') {
      return [value];
    }
    return [];
  })
  skill: string[];

  @IsOptional()
  @IsUrl()
  companyWebsite: string;

  @IsOptional()
  @IsString()
  details: string;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  active: boolean;
}
