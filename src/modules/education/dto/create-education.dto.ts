import {
  IsBoolean,
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateEducationDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  degree: string;

  @IsString()
  @IsOptional()
  logo: string;

  @IsString()
  @IsOptional()
  location: string;

  @IsNotEmpty()
  @IsDateString()
  startedAt: Date;

  @IsOptional()
  @IsDateString()
  endedAt: Date;

  @IsOptional()
  @IsUrl()
  link: string;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  active: boolean;
}
