name: Deploy to Production

on:
  push:
    branches: ["main"]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.12.0  # use your correct version here

      - name: Install dependencies and build
        run: |
          yarn install
          yarn build

      - name: Install SSH client
        run: |
          sudo apt-get update
          sudo apt-get install -y openssh-client

      - name: Setup SSH keys
        run: |
          mkdir -p ~/.ssh
          chmod 700 ~/.ssh
          echo "${{ secrets.EC2_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          echo "${{ secrets.SSH_KNOWN_HOSTS }}" > ~/.ssh/known_hosts
          chmod 644 ~/.ssh/known_hosts

      - name: Upload built app to EC2
        run: |
          scp -r -o StrictHostKeyChecking=no ./dist ubuntu@${{ secrets.EC2_HOST }}:~/admin-frontend/

      - name: Restart App on EC2
        run: |
          ssh -o StrictHostKeyChecking=no ubuntu@${{ secrets.EC2_HOST }} << 'EOF'
          pm2 restart admin-frontend
          EOF
