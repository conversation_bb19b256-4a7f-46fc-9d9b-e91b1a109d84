/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/shared/EducationCard/EducationCard.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Friday, March 17th 2023, 8:01:36 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */

import Image from 'next/image';
import {dateRangeModifier} from '../ExperienceCard/ExperienceCard';
import './index.css';
import {defaultImage} from '@/app/projects/page';

const EducationCard = ({ item }) => {
  // TODO : fix the design of this card
  return (
    <div className="education-card">
      <div className="flex gap-5 border-down items-center">
        {/* insert an image here for next js  */}
        <Image className="content-image" width={100} height={100} src={item.logo || defaultImage} alt=" education" />
        <span className="edu-title">{item.name}</span>
      </div>

      <div className="p-6">
        <span className="">{item.degree}</span>
        <br />
        <br />
        <span className="timeline-btn ">{dateRangeModifier(item.startedAt, item.endedAt)}</span>
      </div>
    </div>
  );
};

export default EducationCard;
