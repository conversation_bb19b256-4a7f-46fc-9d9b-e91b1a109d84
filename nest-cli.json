{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": true}, "projects": {"db": {"type": "library", "root": "libs/db", "entryFile": "index", "sourceRoot": "libs/db/src", "compilerOptions": {"tsConfigPath": "libs/db/tsconfig.lib.json"}}, "repository": {"type": "library", "root": "libs/repository", "entryFile": "index", "sourceRoot": "libs/repository/src", "compilerOptions": {"tsConfigPath": "libs/repository/tsconfig.lib.json"}}, "shared": {"type": "library", "root": "libs/shared", "entryFile": "index", "sourceRoot": "libs/shared/src", "compilerOptions": {"tsConfigPath": "libs/shared/tsconfig.lib.json"}}, "common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}}}